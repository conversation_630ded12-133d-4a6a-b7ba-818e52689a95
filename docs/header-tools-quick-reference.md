# OA Framework Header Tools - Quick Reference

**Copyright**: Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
**Authority**: President & CEO, E.Z. Consultancy
**Version**: 1.0.0  
**Date**: 2025-09-12  

---

## 🚀 **Quick Commands**

### **✅ CORRECT SYNTAX (Use `--` to separate npm and script arguments)**

```bash
# Header Validation
npm run header:validate
npm run header:validate -- --verbose
npm run header:validate -- --pattern "server/src/**/*.ts"
npm run header:validate -- --report

# Header Injection
npm run header:inject
npm run header:inject -- --file "path/to/file.ts"
npm run header:inject -- --file "path/to/file.ts" --componentName "MyComponent"

# Compliance Checking
npm run header:check
npm run header:check -- --m01
npm run header:check -- --file "path/to/file.ts"
npm run header:check -- --pre-commit

# Header Generation
npm run header:generate
npm run header:generate -- --quick
npm run header:generate -- --template enhanced-service
npm run header:generate -- --list
```

### **❌ INCORRECT SYNTAX (Will not work)**

```bash
# These will NOT work - missing the "--" separator
npm run header:inject --file "path/to/file.ts"          # ❌ Wrong
npm run header:check --m01                              # ❌ Wrong
npm run header:validate --verbose                       # ❌ Wrong
```

---

## 📋 **Common Use Cases**

### **1. Fix Single File**
```bash
# Check what's wrong
npm run header:check -- --file "server/src/MyComponent.ts"

# Inject header
npm run header:inject -- --file "server/src/MyComponent.ts"
```

### **2. Check M0.1 Milestone Compliance**
```bash
# Quick compliance check
npm run header:check -- --m01

# Detailed validation
npm run header:validate -- --pattern "server/src/platform/**/*.ts"
```

### **3. Generate Header for New Component**
```bash
# Interactive wizard
npm run header:generate

# Quick generation
npm run header:generate -- --quick

# Use specific template
npm run header:generate -- --template enhanced-service
```

### **4. Batch Processing**
```bash
# Find and fix all non-compliant files
find server/src -name "*.ts" -not -path "*/node_modules/*" -not -name "*.test.ts" | while read file; do
  if ! grep -q "OA FRAMEWORK - TYPESCRIPT SOURCE FILE" "$file"; then
    echo "Injecting header into: $file"
    npm run header:inject -- --file "$file"
  fi
done
```

---

## 🔧 **Tool-Specific Options**

### **Header Injection (`inject-header.js`)**
```bash
npm run header:inject -- --file "path/to/file.ts" \
  --componentName "MyComponent" \
  --purpose "Component purpose" \
  --milestone "M0.1" \
  --taskId "TSK-001" \
  --template "enhanced-service"
```

### **Header Validation (`validate-headers.js`)**
```bash
npm run header:validate -- --pattern "server/src/**/*.ts" \
  --exclude "node_modules/**,dist/**" \
  --verbose \
  --report
```

### **Compliance Checker (`check-compliance.js`)**
```bash
npm run header:check -- --m01 --verbose
npm run header:check -- --file "path/to/file.ts"
npm run header:check -- --pre-commit
```

### **Header Generator (`generate-header.js`)**
```bash
npm run header:generate -- --template enhanced-service \
  --componentName "MyComponent" \
  --purpose "Component purpose"
```

---

## 🎯 **Templates Available**

| Template | Use Case |
|----------|----------|
| `enhanced-service` | Complex server-side services |
| `base-component` | Foundation components |
| `tracking-component` | Tracking system components |
| `governance-component` | Governance system components |
| `client-component` | Client-side components |

```bash
# List all available templates
npm run header:generate -- --list
```

---

## 📊 **Understanding Output**

### **Compliance Scores**
- 🟢 **100%**: Fully compliant
- 🟡 **75-99%**: Minor issues
- 🔴 **0-74%**: Major compliance issues

### **Common Violations**
- Missing copyright notice (Critical)
- Missing required sections
- Missing metadata fields
- Missing presidential authority

---

## 🚨 **Critical Legal Requirement**

**ALL files MUST include:**
```
Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
```

**Files missing copyright are flagged as CRITICAL LEGAL ISSUES.**

---

## 💡 **Pro Tips**

1. **Always use `--`** to separate npm arguments from script arguments
2. **Check before inject**: Use `header:check` to see what's missing
3. **Use templates**: Save time with predefined component templates
4. **Batch processing**: Use shell scripts for multiple files
5. **Pre-commit hooks**: Integrate with git for automatic validation

---

## 🔍 **Troubleshooting**

### **Command Not Working?**
- ✅ Make sure you use `--` separator
- ✅ Check file paths are correct
- ✅ Verify npm scripts are installed

### **ESLint Not Detecting Issues?**
```bash
# Verify ESLint plugin is working
npm run lint -- --no-fix server/src/platform/tracking/core-utils/TrackingUtilities.ts
```

### **Need Help?**
```bash
# Show usage for any tool
node scripts/header-tools/inject-header.js
node scripts/header-tools/validate-headers.js
node scripts/header-tools/check-compliance.js
node scripts/header-tools/generate-header.js
```

---

## 📈 **Current Status**

**M0.1 Milestone Compliance**: 0% (236 files need headers)  
**Priority**: Address copyright notices first (legal requirement)  
**Next Steps**: Begin systematic header injection

---

**Remember**: The `--` separator is REQUIRED when passing arguments to npm scripts!

✅ **Correct**: `npm run header:inject -- --file "path/to/file.ts"`  
❌ **Wrong**: `npm run header:inject --file "path/to/file.ts"`
