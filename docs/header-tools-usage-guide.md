# OA Framework Unified Header Tools - Usage Guide

**Version**: 1.0.0  
**Created**: 2025-09-12  
**Copyright**: Copyright (c) 2025 E.Z. Consultancy. All rights reserved.

---

## 🎯 **Overview**

This guide provides comprehensive instructions for using the OA Framework Unified Header Format validation and injection tools. These tools ensure 100% compliance with the mandatory unified header format v2.3 across all TypeScript files.

## 📋 **Available Tools**

### **1. ESLint Plugin (`eslint-plugin-oa-header`)**
- Real-time header format validation during development
- Integrated with VSCode and other IDEs
- Automatic violation detection and reporting

### **2. Header Injection Tool (`inject-header.js`)**
- Automated header injection for new files
- Interactive and batch processing modes
- Backup creation for safety

### **3. Header Validation Tool (`validate-headers.js`)**
- Comprehensive compliance checking
- Detailed violation reporting
- Compliance scoring and metrics

### **4. Compliance Checker (`check-compliance.js`)**
- Quick compliance assessment
- Actionable fix recommendations
- Pre-commit validation support

### **5. Header Generator (`generate-header.js`)**
- Interactive header creation wizard
- Template-based generation
- Custom template management

---

## 🚀 **Quick Start**

### **Step 1: Validate Current Files**
```bash
# Check all TypeScript files for compliance
npm run header:validate

# Check specific directory
npm run header:check --dir server/src

# Check M0.1 milestone files specifically
npm run header:check --m01
```

### **Step 2: Generate Headers for New Files**
```bash
# Interactive header generation
npm run header:generate

# Quick generation with minimal prompts
npm run header:generate --quick

# Generate from template
npm run header:generate --template enhanced-service
```

### **Step 3: Inject Headers into Existing Files**
```bash
# Interactive injection
npm run header:inject

# Inject into specific file
npm run header:inject -- --file "server/src/MyComponent.ts"

# Batch injection with options
npm run header:inject -- --file "server/src/MyComponent.ts" --componentName "MyComponent" --purpose "Component purpose"
```

---

## 📖 **Detailed Usage Instructions**

### **ESLint Integration**

#### **Real-time Validation**
The ESLint plugin automatically validates headers as you type:

```bash
# Run ESLint with header validation
npm run lint

# Fix auto-fixable issues
npm run lint:fix
```

#### **ESLint Rules Configuration**
The following rules are automatically enabled:
- `oa-header/unified-header-format`: Validates complete header structure
- `oa-header/copyright-notice`: Ensures mandatory copyright notice
- `oa-header/presidential-authority`: Validates governance authority

#### **Disable Rules for Test Files**
Test files automatically have header validation disabled to avoid unnecessary overhead.

### **Header Validation Tool**

#### **Basic Validation**
```bash
# Validate all TypeScript files
npm run header:validate

# Validate with verbose output
npm run header:validate --verbose

# Generate compliance report
npm run header:validate --report
```

#### **Pattern-based Validation**
```bash
# Validate specific pattern
npm run header:validate --pattern "server/src/**/*.ts"

# Exclude specific directories
npm run header:validate --pattern "**/*.ts" --exclude "node_modules/**,dist/**"
```

#### **Understanding Validation Output**
```
✅ server/src/CompliantFile.ts 🟢 100%
❌ server/src/NonCompliantFile.ts 🔴 45%
   ❌ Missing required section: AUTHORITY-DRIVEN GOVERNANCE (v2.3)
   ❌ Missing mandatory copyright notice
   ❌ Missing required metadata: @task-id
```

### **Header Injection Tool**

#### **Interactive Mode**
```bash
npm run header:inject
```
Follow the prompts to provide:
- Component Name
- Component Purpose
- File Path
- Milestone
- Task ID
- Tier (server/client/shared)
- Category

#### **File-specific Injection (PRESERVES EXISTING METADATA)**
```bash
# Basic injection - AUTOMATICALLY PRESERVES original metadata
npm run header:inject -- --file "path/to/file.ts"

# With custom options - overrides preserved values only when specified
npm run header:inject -- --file "path/to/file.ts" \
  --componentName "MyComponent" \
  --purpose "Component purpose" \
  --milestone "M0.1" \
  --taskId "TSK-001"
```

#### **🔄 Metadata Preservation (NEW FEATURE)**
The injection tool now **automatically preserves** existing metadata:
- **Task IDs, component names, references**
- **Tier, context, category information**
- **Created dates, component types**
- **Dependencies, related contexts**
- **Authority levels, governance references**
- **Lifecycle stage, testing status**

**Example Output:**
```
✅ Header injected into server/src/MyComponent.ts
📁 Backup saved as server/src/MyComponent.ts.backup
🔄 Preserved metadata: taskId, component, reference, tier, context, category, created
```

#### **Safety Features**
- Automatic backup creation (`.backup` extension)
- Existing header detection and replacement
- Intelligent metadata preservation
- Validation before injection

### **Compliance Checker**

#### **Quick Compliance Check**
```bash
# Check all files with recommendations
npm run header:check

# Check specific file
npm run header:check --file "server/src/MyComponent.ts"

# Check M0.1 milestone files
npm run header:check --m01

# Pre-commit validation
npm run header:check --pre-commit
```

#### **Understanding Compliance Output**
```
🔍 Checking: server/src/MyComponent.ts
==================================================
❌ NON-COMPLIANT 🔴 65%

🚨 Violations:
   1. Missing required section: MEMORY SAFETY & TIMING RESILIENCE (v2.3)
   2. Missing mandatory copyright notice

🔧 Recommended Fix:
   npm run header:inject -- --file "server/src/MyComponent.ts"
```

### **Header Generator**

#### **Interactive Wizard**
```bash
npm run header:generate
```
The wizard guides you through:
1. Template selection (enhanced-service, base-component, etc.)
2. Component information
3. Configuration options
4. Advanced settings
5. Save options

#### **Quick Generation**
```bash
npm run header:generate --quick
```
Minimal prompts for rapid header creation.

#### **Template Management**
```bash
# List available templates
npm run header:generate --list

# Generate from specific template
npm run header:generate --template enhanced-service

# Save custom template during wizard
# (option provided during interactive generation)
```

---

## 🔧 **Advanced Usage**

### **Batch Processing**

#### **Validate Multiple Directories**
```bash
# Create validation script
cat > validate-all.sh << 'EOF'
#!/bin/bash
npm run header:validate --pattern "server/src/**/*.ts"
npm run header:validate --pattern "shared/src/**/*.ts"
npm run header:validate --pattern "client/src/**/*.ts"
EOF

chmod +x validate-all.sh
./validate-all.sh
```

#### **Batch Header Injection**
```bash
# Find files without headers and inject
find server/src -name "*.ts" -not -path "*/node_modules/*" -not -name "*.test.ts" | while read file; do
  if ! grep -q "OA FRAMEWORK - TYPESCRIPT SOURCE FILE" "$file"; then
    echo "Injecting header into: $file"
    npm run header:inject -- --file "$file"
  fi
done
```

### **Custom Templates**

#### **Creating Custom Templates**
1. Run the header generator wizard: `npm run header:generate`
2. Configure your desired settings
3. Choose "Save as reusable template" at the end
4. Provide a template name

#### **Using Custom Templates**
```bash
# List all templates (built-in + custom)
npm run header:generate --list

# Use custom template
npm run header:generate --template my-custom-template
```

### **Integration with Development Workflow**

#### **Pre-commit Hook Setup**
Add to `.git/hooks/pre-commit`:
```bash
#!/bin/bash
echo "🔍 Checking header compliance..."
npm run header:check --pre-commit
if [ $? -ne 0 ]; then
  echo "❌ Commit blocked due to header compliance violations"
  echo "Fix violations and try again"
  exit 1
fi
echo "✅ Header compliance check passed"
```

#### **VSCode Integration**
The ESLint plugin automatically integrates with VSCode:
1. Install ESLint extension
2. Header violations appear as red squiggles
3. Problems panel shows detailed violation messages
4. Auto-fix suggestions where applicable

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **ESLint Plugin Not Working**
```bash
# Verify plugin installation
ls scripts/header-tools/eslint-plugin-oa-header.js

# Check ESLint configuration
cat .eslintrc.json | grep "oa-header"

# Restart VSCode/IDE after configuration changes
```

#### **Validation Tool Errors**
```bash
# Check Node.js version (requires Node 16+)
node --version

# Verify dependencies
npm list glob

# Run with debug output
DEBUG=* npm run header:validate
```

#### **Header Injection Failures**
```bash
# Check file permissions
ls -la path/to/file.ts

# Verify file exists and is writable
test -w path/to/file.ts && echo "Writable" || echo "Not writable"

# Check for backup files
ls path/to/file.ts.backup
```

### **Performance Optimization**

#### **Large Codebase Validation**
```bash
# Use pattern-based validation for large codebases
npm run header:validate --pattern "server/src/specific-module/**/*.ts"

# Exclude unnecessary directories
npm run header:validate --pattern "**/*.ts" --exclude "node_modules/**,dist/**,coverage/**"
```

#### **Parallel Processing**
```bash
# Split validation across multiple processes
npm run header:validate --pattern "server/**/*.ts" &
npm run header:validate --pattern "shared/**/*.ts" &
npm run header:validate --pattern "client/**/*.ts" &
wait
```

---

## 📊 **Compliance Monitoring**

### **Regular Compliance Checks**
```bash
# Weekly compliance report
npm run header:validate --report > weekly-compliance-$(date +%Y-%m-%d).json

# Compliance dashboard (basic)
npm run header:check --m01 | grep "Compliance Rate"
```

### **Metrics Tracking**
The validation tools provide detailed metrics:
- **Compliance Percentage**: Overall project compliance
- **Violation Count**: Total number of violations
- **File Coverage**: Number of files checked
- **Score Distribution**: Compliance score breakdown

---

## 🎯 **Best Practices**

### **Development Workflow**
1. **Before Creating New Files**: Use `npm run header:generate` to create compliant headers
2. **During Development**: ESLint provides real-time validation
3. **Before Committing**: Run `npm run header:check --pre-commit`
4. **Weekly Reviews**: Generate compliance reports for tracking

### **Team Collaboration**
1. **Onboarding**: Ensure all team members understand header requirements
2. **Code Reviews**: Include header compliance in review checklist
3. **Documentation**: Keep this guide updated with team-specific practices
4. **Training**: Regular training sessions on header format importance

### **Maintenance**
1. **Regular Updates**: Keep tools updated with latest requirements
2. **Template Management**: Maintain and update custom templates
3. **Compliance Monitoring**: Track compliance trends over time
4. **Tool Enhancement**: Gather feedback and improve tools

---

## 📞 **Support**

### **Getting Help**
- **Tool Issues**: Check troubleshooting section above
- **Format Questions**: Review `docs/hand-off-docs/unified-header-hndoff.md`
- **Governance Issues**: Contact governance team for policy clarification

### **Contributing**
- **Bug Reports**: Document issues with reproduction steps
- **Feature Requests**: Propose enhancements with business justification
- **Tool Improvements**: Submit improvements following development standards

---

**Authority**: President & CEO, E.Z. Consultancy  
**Status**: ✅ **READY FOR USE**  
**Last Updated**: 2025-09-12  
**Next Review**: 2025-10-12
