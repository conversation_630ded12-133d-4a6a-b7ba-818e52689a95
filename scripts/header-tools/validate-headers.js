#!/usr/bin/env node

/**
 * ============================================================================
 * OA FRAMEWORK - Header Validation Tool
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file Header Validation Tool for OA Framework
 * @description Comprehensive validation tool for unified header format compliance
 * @version 1.0.0
 * @created 2025-09-12
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Required sections for unified header format v2.3
 */
const REQUIRED_SECTIONS = [
  'AI CONTEXT:',
  'OA FRAMEWORK - TYPESCRIPT SOURCE FILE (v2.3)',
  'Copyright (c) 2025 E.Z. Consultancy. All rights reserved.',
  'AUTHORITY-DRIVEN GOVERNANCE (v2.3)',
  'CROSS-CONTEXT REFERENCES (v2.3)',
  'MEMORY SAFETY & TIMING RESILIENCE (v2.3)',
  'GATEWAY INTEGRATION (v2.3)',
  'SECURITY CLASSIFICATION (v2.3)',
  'PERFORMANCE REQUIREMENTS (v2.3)',
  'INTEGRATION REQUIREMENTS (v2.3)',
  'ENHANCED METADATA (v2.3)',
  'ORCHESTRATION METADATA (v2.3)',
  'VERSION HISTORY (v2.3)'
];

/**
 * Required metadata fields
 */
const REQUIRED_METADATA = [
  '@file',
  '@filepath',
  '@milestone',
  '@task-id',
  '@component',
  '@reference',
  '@template',
  '@tier',
  '@context',
  '@category',
  '@created',
  '@modified',
  '@version',
  '@description'
];

/**
 * Required governance fields
 */
const REQUIRED_GOVERNANCE = [
  '@authority-level',
  '@authority-validator',
  '@governance-adr',
  '@governance-dcr',
  '@governance-status',
  '@governance-compliance'
];

/**
 * Validate a single file's header
 */
function validateFileHeader(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const violations = [];
  const warnings = [];

  // Check if file has any header
  if (!content.startsWith('/**')) {
    violations.push('Missing JSDoc header block');
    return { violations, warnings, score: 0 };
  }

  // Extract ALL header content (both AI Context and main header blocks)
  // Find the end of the last header block before actual code starts
  const lines = content.split('\n');
  let headerEndLine = 0;
  let inHeader = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('/**')) {
      inHeader = true;
    } else if (line.endsWith('*/') && inHeader) {
      headerEndLine = i;
      inHeader = false;
    } else if (!inHeader && line && !line.startsWith('//') && !line.startsWith('*')) {
      // Found actual code, stop looking for headers
      break;
    }
  }

  const header = lines.slice(0, headerEndLine + 1).join('\n');

  // Check required sections
  let sectionsFound = 0;
  REQUIRED_SECTIONS.forEach(section => {
    if (header.includes(section)) {
      sectionsFound++;
    } else {
      violations.push(`Missing required section: ${section}`);
    }
  });

  // Check required metadata
  let metadataFound = 0;
  REQUIRED_METADATA.forEach(meta => {
    if (header.includes(meta)) {
      metadataFound++;
    } else {
      violations.push(`Missing required metadata: ${meta}`);
    }
  });

  // Check governance fields
  let governanceFound = 0;
  REQUIRED_GOVERNANCE.forEach(gov => {
    if (header.includes(gov)) {
      governanceFound++;
    } else {
      violations.push(`Missing required governance field: ${gov}`);
    }
  });

  // Check specific requirements
  if (!header.includes('President & CEO, E.Z. Consultancy')) {
    violations.push('Missing mandatory presidential authority validation');
  }

  if (!header.includes('(v2.3)')) {
    violations.push('Missing v2.3 format compliance indicators');
  }

  // Check for proper copyright
  if (!header.includes('Copyright (c) 2025 E.Z. Consultancy. All rights reserved.')) {
    violations.push('Missing or incorrect copyright notice');
  }

  // Calculate compliance score
  const totalRequirements = REQUIRED_SECTIONS.length + REQUIRED_METADATA.length + REQUIRED_GOVERNANCE.length + 3; // +3 for authority, v2.3, copyright
  const foundRequirements = sectionsFound + metadataFound + governanceFound + 
    (header.includes('President & CEO, E.Z. Consultancy') ? 1 : 0) +
    (header.includes('(v2.3)') ? 1 : 0) +
    (header.includes('Copyright (c) 2025 E.Z. Consultancy. All rights reserved.') ? 1 : 0);
  
  const score = Math.round((foundRequirements / totalRequirements) * 100);

  // Add warnings for best practices
  if (!header.includes('AI Navigation:')) {
    warnings.push('Missing AI Navigation information for better AI assistance');
  }

  if (!header.includes('Lines: Target')) {
    warnings.push('Missing target line count information');
  }

  return { violations, warnings, score };
}

/**
 * Validate multiple files
 */
function validateFiles(pattern = '**/*.ts', options = {}) {
  const {
    exclude = ['node_modules/**', 'dist/**', '**/*.test.ts', '**/__tests__/**'],
    verbose = false,
    summary = true
  } = options;

  console.log('🔍 OA Framework Header Validation');
  console.log('==================================\n');

  const files = glob.sync(pattern, { ignore: exclude });
  
  if (files.length === 0) {
    console.log('❌ No TypeScript files found matching pattern:', pattern);
    return;
  }

  console.log(`📁 Found ${files.length} TypeScript files to validate\n`);

  const results = {
    total: files.length,
    compliant: 0,
    nonCompliant: 0,
    totalViolations: 0,
    totalWarnings: 0,
    files: []
  };

  files.forEach(file => {
    const validation = validateFileHeader(file);
    const isCompliant = validation.violations.length === 0;
    
    results.files.push({
      file,
      ...validation,
      compliant: isCompliant
    });

    if (isCompliant) {
      results.compliant++;
    } else {
      results.nonCompliant++;
    }

    results.totalViolations += validation.violations.length;
    results.totalWarnings += validation.warnings.length;

    // Output file results
    if (verbose || !isCompliant) {
      const status = isCompliant ? '✅' : '❌';
      const scoreColor = validation.score >= 90 ? '🟢' : validation.score >= 70 ? '🟡' : '🔴';
      
      console.log(`${status} ${file} ${scoreColor} ${validation.score}%`);
      
      if (validation.violations.length > 0) {
        validation.violations.forEach(violation => {
          console.log(`   ❌ ${violation}`);
        });
      }
      
      if (validation.warnings.length > 0 && verbose) {
        validation.warnings.forEach(warning => {
          console.log(`   ⚠️  ${warning}`);
        });
      }
      
      if (!isCompliant || verbose) {
        console.log('');
      }
    }
  });

  // Summary report
  if (summary) {
    console.log('\n📊 Validation Summary');
    console.log('=====================');
    console.log(`Total files: ${results.total}`);
    console.log(`✅ Compliant: ${results.compliant} (${Math.round(results.compliant / results.total * 100)}%)`);
    console.log(`❌ Non-compliant: ${results.nonCompliant} (${Math.round(results.nonCompliant / results.total * 100)}%)`);
    console.log(`🚨 Total violations: ${results.totalViolations}`);
    console.log(`⚠️  Total warnings: ${results.totalWarnings}`);
    
    const overallScore = Math.round(results.compliant / results.total * 100);
    const scoreEmoji = overallScore >= 90 ? '🟢' : overallScore >= 70 ? '🟡' : '🔴';
    console.log(`\n${scoreEmoji} Overall Compliance: ${overallScore}%`);
    
    if (results.nonCompliant > 0) {
      console.log('\n🔧 Remediation Suggestions:');
      console.log('1. Run: npm run header:inject --file <filename> to add headers');
      console.log('2. Use: npm run header:generate for interactive header creation');
      console.log('3. Check: docs/hand-off-docs/unified-header-hndoff.md for format details');
    }
  }

  return results;
}

/**
 * Generate detailed compliance report
 */
function generateComplianceReport(results, outputPath = 'header-compliance-report.json') {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: results.total,
      compliantFiles: results.compliant,
      nonCompliantFiles: results.nonCompliant,
      compliancePercentage: Math.round(results.compliant / results.total * 100),
      totalViolations: results.totalViolations,
      totalWarnings: results.totalWarnings
    },
    files: results.files,
    recommendations: [
      'Use npm run header:inject to add unified headers to non-compliant files',
      'Review docs/hand-off-docs/unified-header-hndoff.md for format specifications',
      'Enable ESLint rules for continuous compliance monitoring',
      'Schedule regular header compliance audits'
    ]
  };

  fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 Detailed report saved to: ${outputPath}`);
  
  return report;
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  let pattern = '**/*.ts';
  let options = {
    verbose: false,
    summary: true,
    report: false
  };

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--pattern':
        pattern = args[++i];
        break;
      case '--verbose':
        options.verbose = true;
        break;
      case '--no-summary':
        options.summary = false;
        break;
      case '--report':
        options.report = true;
        break;
      case '--help':
        console.log('OA Framework Header Validation Tool');
        console.log('Usage: node validate-headers.js [options]');
        console.log('');
        console.log('Options:');
        console.log('  --pattern <glob>    File pattern to validate (default: **/*.ts)');
        console.log('  --verbose           Show detailed output for all files');
        console.log('  --no-summary        Skip summary report');
        console.log('  --report            Generate JSON compliance report');
        console.log('  --help              Show this help message');
        process.exit(0);
    }
  }

  const results = validateFiles(pattern, options);
  
  if (options.report && results) {
    generateComplianceReport(results);
  }
  
  // Exit with error code if there are non-compliant files
  if (results && results.nonCompliant > 0) {
    process.exit(1);
  }
}

module.exports = {
  validateFileHeader,
  validateFiles,
  generateComplianceReport,
  REQUIRED_SECTIONS,
  REQUIRED_METADATA,
  REQUIRED_GOVERNANCE
};
