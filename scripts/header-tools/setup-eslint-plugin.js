#!/usr/bin/env node

/**
 * ============================================================================
 * OA FRAMEWORK - ESLint Plugin Setup Script
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file ESLint Plugin Setup for Local Development
 * @description Sets up the OA Framework header validation ESLint plugin
 * @version 1.0.0
 * @created 2025-09-12
 */

const fs = require('fs');
const path = require('path');

/**
 * Setup ESLint plugin for local development
 */
function setupESLintPlugin() {
  console.log('🔧 Setting up OA Framework ESLint Plugin');
  console.log('========================================\n');

  const projectRoot = path.resolve(__dirname, '../..');
  const pluginPath = path.join(__dirname, 'eslint-plugin-oa-header.js');
  const nodeModulesPath = path.join(projectRoot, 'node_modules');
  const eslintPluginDir = path.join(nodeModulesPath, 'eslint-plugin-oa-header');

  try {
    // Create node_modules directory if it doesn't exist
    if (!fs.existsSync(nodeModulesPath)) {
      fs.mkdirSync(nodeModulesPath, { recursive: true });
      console.log('✅ Created node_modules directory');
    }

    // Create plugin directory in node_modules
    if (!fs.existsSync(eslintPluginDir)) {
      fs.mkdirSync(eslintPluginDir, { recursive: true });
      console.log('✅ Created eslint-plugin-oa-header directory');
    }

    // Copy plugin files
    const pluginContent = fs.readFileSync(pluginPath, 'utf8');
    fs.writeFileSync(path.join(eslintPluginDir, 'index.js'), pluginContent);
    console.log('✅ Copied plugin files');

    // Create package.json for the plugin
    const packageJson = {
      name: 'eslint-plugin-oa-header',
      version: '1.0.0',
      description: 'ESLint plugin for OA Framework unified header format validation',
      main: 'index.js',
      keywords: ['eslint', 'eslintplugin', 'oa-framework', 'header', 'validation'],
      author: 'E.Z. Consultancy',
      license: 'MIT',
      peerDependencies: {
        eslint: '>=8.0.0'
      }
    };

    fs.writeFileSync(
      path.join(eslintPluginDir, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
    console.log('✅ Created plugin package.json');

    // Verify ESLint configuration
    const eslintConfigPath = path.join(projectRoot, '.eslintrc.json');
    if (fs.existsSync(eslintConfigPath)) {
      const eslintConfig = JSON.parse(fs.readFileSync(eslintConfigPath, 'utf8'));
      
      if (eslintConfig.plugins && eslintConfig.plugins.includes('oa-header')) {
        console.log('✅ ESLint configuration already includes oa-header plugin');
      } else {
        console.log('⚠️  ESLint configuration may need manual update');
        console.log('   Add "oa-header" to plugins array in .eslintrc.json');
      }
    }

    console.log('\n🎉 ESLint plugin setup complete!');
    console.log('\nNext steps:');
    console.log('1. Restart your IDE/editor');
    console.log('2. Run: npm run lint');
    console.log('3. Check for header validation errors');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('\nTroubleshooting:');
    console.log('1. Check file permissions');
    console.log('2. Ensure you have write access to node_modules');
    console.log('3. Try running with sudo (if necessary)');
    process.exit(1);
  }
}

/**
 * Verify plugin installation
 */
function verifyInstallation() {
  console.log('🔍 Verifying ESLint Plugin Installation');
  console.log('======================================\n');

  const projectRoot = path.resolve(__dirname, '../..');
  const eslintPluginDir = path.join(projectRoot, 'node_modules', 'eslint-plugin-oa-header');

  const checks = [
    {
      name: 'Plugin directory exists',
      check: () => fs.existsSync(eslintPluginDir),
      fix: 'Run: node scripts/header-tools/setup-eslint-plugin.js'
    },
    {
      name: 'Plugin main file exists',
      check: () => fs.existsSync(path.join(eslintPluginDir, 'index.js')),
      fix: 'Run: node scripts/header-tools/setup-eslint-plugin.js'
    },
    {
      name: 'Plugin package.json exists',
      check: () => fs.existsSync(path.join(eslintPluginDir, 'package.json')),
      fix: 'Run: node scripts/header-tools/setup-eslint-plugin.js'
    },
    {
      name: 'ESLint config includes plugin',
      check: () => {
        const eslintConfigPath = path.join(projectRoot, '.eslintrc.json');
        if (!fs.existsSync(eslintConfigPath)) return false;
        const config = JSON.parse(fs.readFileSync(eslintConfigPath, 'utf8'));
        return config.plugins && config.plugins.includes('oa-header');
      },
      fix: 'Add "oa-header" to plugins array in .eslintrc.json'
    }
  ];

  let allPassed = true;

  checks.forEach(check => {
    const passed = check.check();
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    
    if (!passed) {
      console.log(`   Fix: ${check.fix}`);
      allPassed = false;
    }
  });

  console.log('\n' + '='.repeat(40));
  if (allPassed) {
    console.log('✅ All checks passed! Plugin is ready to use.');
    console.log('\nTest the plugin:');
    console.log('npm run lint');
  } else {
    console.log('❌ Some checks failed. Please fix the issues above.');
  }

  return allPassed;
}

/**
 * Test plugin functionality
 */
function testPlugin() {
  console.log('🧪 Testing ESLint Plugin Functionality');
  console.log('=====================================\n');

  const projectRoot = path.resolve(__dirname, '../..');
  const testFile = path.join(__dirname, 'test-header-validation.ts');

  // Create test file with violations
  const testContent = `// Test file without proper header
export class TestComponent {
  constructor() {
    console.log('Test component');
  }
}`;

  try {
    fs.writeFileSync(testFile, testContent);
    console.log('✅ Created test file');

    // Try to run ESLint on test file
    const { spawn } = require('child_process');
    const eslint = spawn('npx', ['eslint', testFile], {
      cwd: projectRoot,
      stdio: 'pipe'
    });

    let output = '';
    let errorOutput = '';

    eslint.stdout.on('data', (data) => {
      output += data.toString();
    });

    eslint.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    eslint.on('close', (code) => {
      // Clean up test file
      if (fs.existsSync(testFile)) {
        fs.unlinkSync(testFile);
        console.log('✅ Cleaned up test file');
      }

      if (output.includes('oa-header') || errorOutput.includes('oa-header')) {
        console.log('✅ Plugin is working - detected header violations');
        console.log('\nSample output:');
        console.log(output || errorOutput);
      } else {
        console.log('⚠️  Plugin may not be working properly');
        console.log('Output:', output);
        console.log('Error:', errorOutput);
      }
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    // Clean up test file
    if (fs.existsSync(testFile)) {
      fs.unlinkSync(testFile);
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--setup') {
    setupESLintPlugin();
  } else if (args[0] === '--verify') {
    verifyInstallation();
  } else if (args[0] === '--test') {
    testPlugin();
  } else if (args[0] === '--help') {
    console.log('OA Framework ESLint Plugin Setup');
    console.log('Usage: node setup-eslint-plugin.js [options]');
    console.log('');
    console.log('Options:');
    console.log('  --setup     Setup the ESLint plugin (default)');
    console.log('  --verify    Verify plugin installation');
    console.log('  --test      Test plugin functionality');
    console.log('  --help      Show this help message');
  } else {
    console.error(`❌ Unknown option: ${args[0]}`);
    console.log('Use --help for usage information');
  }
}

module.exports = {
  setupESLintPlugin,
  verifyInstallation,
  testPlugin
};
