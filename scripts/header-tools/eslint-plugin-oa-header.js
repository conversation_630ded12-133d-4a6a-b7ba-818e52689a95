/**
 * ============================================================================
 * OA FRAMEWORK - ESLint Plugin for Unified Header Format Validation
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file ESLint Plugin for OA Framework Unified Header Format
 * @description Custom ESLint rules for validating unified header format compliance
 * @version 1.0.0
 * @created 2025-09-12
 */

const fs = require('fs');
const path = require('path');

/**
 * Unified Header Format Validation Rules
 */
const rules = {
  'unified-header-format': {
    meta: {
      type: 'problem',
      docs: {
        description: 'Enforce OA Framework unified header format with all 13 required sections',
        category: 'Stylistic Issues',
        recommended: true
      },
      fixable: null,
      schema: []
    },
    create(context) {
      return {
        Program(node) {
          const sourceCode = context.getSourceCode();
          const text = sourceCode.getText();
          const filename = context.getFilename();
          
          // Skip non-TypeScript files
          if (!filename.endsWith('.ts') && !filename.endsWith('.tsx')) {
            return;
          }
          
          // Skip test files and node_modules
          if (filename.includes('node_modules') || 
              filename.includes('.test.') || 
              filename.includes('__tests__')) {
            return;
          }
          
          const violations = validateUnifiedHeader(text, filename);
          
          violations.forEach(violation => {
            context.report({
              node,
              message: violation.message,
              loc: { line: violation.line || 1, column: 0 }
            });
          });
        }
      };
    }
  },

  'copyright-notice': {
    meta: {
      type: 'problem',
      docs: {
        description: 'Enforce mandatory E.Z. Consultancy copyright notice',
        category: 'Legal',
        recommended: true
      },
      fixable: null,
      schema: []
    },
    create(context) {
      return {
        Program(node) {
          const sourceCode = context.getSourceCode();
          const text = sourceCode.getText();
          const filename = context.getFilename();
          
          // Skip non-TypeScript files
          if (!filename.endsWith('.ts') && !filename.endsWith('.tsx')) {
            return;
          }
          
          // Skip test files and node_modules
          if (filename.includes('node_modules') || 
              filename.includes('.test.') || 
              filename.includes('__tests__')) {
            return;
          }
          
          const requiredCopyright = 'Copyright (c) 2025 E.Z. Consultancy. All rights reserved.';
          
          if (!text.includes(requiredCopyright)) {
            context.report({
              node,
              message: `Missing mandatory copyright notice: "${requiredCopyright}"`,
              loc: { line: 1, column: 0 }
            });
          }
        }
      };
    }
  },

  'presidential-authority': {
    meta: {
      type: 'problem',
      docs: {
        description: 'Enforce presidential authority validation in governance section',
        category: 'Governance',
        recommended: true
      },
      fixable: null,
      schema: []
    },
    create(context) {
      return {
        Program(node) {
          const sourceCode = context.getSourceCode();
          const text = sourceCode.getText();
          const filename = context.getFilename();
          
          // Skip non-TypeScript files
          if (!filename.endsWith('.ts') && !filename.endsWith('.tsx')) {
            return;
          }
          
          // Skip test files and node_modules
          if (filename.includes('node_modules') || 
              filename.includes('.test.') || 
              filename.includes('__tests__')) {
            return;
          }
          
          const requiredAuthority = 'President & CEO, E.Z. Consultancy';
          
          if (!text.includes(requiredAuthority)) {
            context.report({
              node,
              message: `Missing mandatory presidential authority: "${requiredAuthority}"`,
              loc: { line: 1, column: 0 }
            });
          }
        }
      };
    }
  }
};

/**
 * Validate unified header format compliance
 */
function validateUnifiedHeader(text, filename) {
  const violations = [];
  const lines = text.split('\n');
  
  // Required sections in order
  const requiredSections = [
    'AI CONTEXT:',
    'OA FRAMEWORK - TYPESCRIPT SOURCE FILE (v2.3)',
    'Copyright (c) 2025 E.Z. Consultancy. All rights reserved.',
    'AUTHORITY-DRIVEN GOVERNANCE (v2.3)',
    'CROSS-CONTEXT REFERENCES (v2.3)',
    'MEMORY SAFETY & TIMING RESILIENCE (v2.3)',
    'GATEWAY INTEGRATION (v2.3)',
    'SECURITY CLASSIFICATION (v2.3)',
    'PERFORMANCE REQUIREMENTS (v2.3)',
    'INTEGRATION REQUIREMENTS (v2.3)',
    'ENHANCED METADATA (v2.3)',
    'ORCHESTRATION METADATA (v2.3)',
    'VERSION HISTORY (v2.3)'
  ];
  
  // Check for header start
  const headerStart = text.indexOf('/**');
  if (headerStart === -1) {
    violations.push({
      message: 'Missing JSDoc header block',
      line: 1
    });
    return violations;
  }
  
  // Check each required section
  requiredSections.forEach((section, index) => {
    if (!text.includes(section)) {
      violations.push({
        message: `Missing required header section: ${section}`,
        line: 1
      });
    }
  });
  
  // Check v2.3 format compliance
  if (!text.includes('(v2.3)')) {
    violations.push({
      message: 'Missing v2.3 format compliance indicators',
      line: 1
    });
  }
  
  // Check for proper file metadata
  const requiredMetadata = ['@file', '@filepath', '@milestone', '@task-id'];
  requiredMetadata.forEach(meta => {
    if (!text.includes(meta)) {
      violations.push({
        message: `Missing required metadata: ${meta}`,
        line: 1
      });
    }
  });
  
  return violations;
}

module.exports = {
  rules,
  configs: {
    recommended: {
      plugins: ['oa-header'],
      rules: {
        'oa-header/unified-header-format': 'error',
        'oa-header/copyright-notice': 'error',
        'oa-header/presidential-authority': 'error'
      }
    }
  }
};
