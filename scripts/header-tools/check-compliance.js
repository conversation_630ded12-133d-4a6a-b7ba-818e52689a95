#!/usr/bin/env node

/**
 * ============================================================================
 * OA FRAMEWORK - Header Compliance Checker
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file Header Compliance Checker for OA Framework
 * @description Quick compliance checking tool with actionable recommendations
 * @version 1.0.0
 * @created 2025-09-12
 */

const fs = require('fs');
const path = require('path');
const { validateFiles, validateFileHeader } = require('./validate-headers');

/**
 * Quick compliance check for specific directories
 */
function checkDirectoryCompliance(directory) {
  console.log(`🔍 Checking compliance for: ${directory}`);
  console.log('='.repeat(50));
  
  const pattern = path.join(directory, '**/*.ts');
  const results = validateFiles(pattern, { 
    verbose: false, 
    summary: true,
    exclude: ['node_modules/**', 'dist/**', '**/*.test.ts', '**/__tests__/**', '**/*.d.ts']
  });
  
  return results;
}

/**
 * Check compliance for M0.1 milestone files specifically
 */
function checkM01Compliance() {
  console.log('🎯 M0.1 Milestone Compliance Check');
  console.log('==================================\n');
  
  const m01Patterns = [
    'server/src/platform/**/*.ts',
    'shared/src/**/*.ts',
    'client/src/**/*.ts'
  ];
  
  let totalResults = {
    total: 0,
    compliant: 0,
    nonCompliant: 0,
    totalViolations: 0,
    totalWarnings: 0,
    files: []
  };
  
  m01Patterns.forEach(pattern => {
    console.log(`\n📁 Checking pattern: ${pattern}`);
    const results = validateFiles(pattern, { 
      verbose: false, 
      summary: false,
      exclude: ['**/*.test.ts', '**/__tests__/**', '**/*.d.ts']
    });
    
    if (results) {
      totalResults.total += results.total;
      totalResults.compliant += results.compliant;
      totalResults.nonCompliant += results.nonCompliant;
      totalResults.totalViolations += results.totalViolations;
      totalResults.totalWarnings += results.totalWarnings;
      totalResults.files.push(...results.files);
    }
  });
  
  // M0.1 specific summary
  console.log('\n📊 M0.1 Milestone Summary');
  console.log('=========================');
  console.log(`Total M0.1 files: ${totalResults.total}`);
  console.log(`✅ Compliant: ${totalResults.compliant}`);
  console.log(`❌ Non-compliant: ${totalResults.nonCompliant}`);
  
  const complianceRate = totalResults.total > 0 ? Math.round(totalResults.compliant / totalResults.total * 100) : 0;
  console.log(`📈 M0.1 Compliance Rate: ${complianceRate}%`);
  
  if (complianceRate < 100) {
    console.log('\n🚨 M0.1 Compliance Issues Detected');
    console.log('Action Required: Update headers before M0.1 milestone completion');
  }
  
  return totalResults;
}

/**
 * Generate quick fix recommendations
 */
function generateQuickFixes(results) {
  if (!results || results.nonCompliant === 0) {
    console.log('\n✅ All files are compliant! No fixes needed.');
    return;
  }
  
  console.log('\n🔧 Quick Fix Recommendations');
  console.log('============================');
  
  const nonCompliantFiles = results.files.filter(f => !f.compliant);
  
  // Group by common issues
  const issueGroups = {
    missingHeader: [],
    missingCopyright: [],
    missingSections: [],
    missingMetadata: []
  };
  
  nonCompliantFiles.forEach(file => {
    const violations = file.violations.join(' ');
    
    if (violations.includes('Missing JSDoc header')) {
      issueGroups.missingHeader.push(file.file);
    } else if (violations.includes('copyright')) {
      issueGroups.missingCopyright.push(file.file);
    } else if (violations.includes('section')) {
      issueGroups.missingSections.push(file.file);
    } else if (violations.includes('metadata')) {
      issueGroups.missingMetadata.push(file.file);
    }
  });
  
  // Provide specific fix commands
  if (issueGroups.missingHeader.length > 0) {
    console.log('\n📝 Files Missing Headers (Need Complete Header Injection):');
    issueGroups.missingHeader.slice(0, 5).forEach(file => {
      console.log(`   npm run header:inject --file "${file}"`);
    });
    if (issueGroups.missingHeader.length > 5) {
      console.log(`   ... and ${issueGroups.missingHeader.length - 5} more files`);
    }
  }
  
  if (issueGroups.missingCopyright.length > 0) {
    console.log('\n©️  Files Missing Copyright (Critical Legal Issue):');
    issueGroups.missingCopyright.slice(0, 3).forEach(file => {
      console.log(`   ⚠️  ${file} - IMMEDIATE ATTENTION REQUIRED`);
    });
  }
  
  if (issueGroups.missingSections.length > 0) {
    console.log('\n📋 Files Missing Required Sections:');
    console.log('   Recommend: Manual review and header update');
    issueGroups.missingSections.slice(0, 3).forEach(file => {
      console.log(`   📄 ${file}`);
    });
  }
  
  console.log('\n🎯 Batch Fix Commands:');
  console.log('======================');
  console.log('1. Validate all files:     npm run header:validate');
  console.log('2. Generate new header:    npm run header:generate');
  console.log('3. Check specific file:    npm run header:check --pattern "path/to/file.ts"');
  console.log('4. Full compliance report: npm run header:validate --report');
}

/**
 * Check single file compliance
 */
function checkSingleFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.error(`❌ File not found: ${filePath}`);
    return false;
  }
  
  console.log(`🔍 Checking: ${filePath}`);
  console.log('='.repeat(50));
  
  const result = validateFileHeader(filePath);
  
  const status = result.violations.length === 0 ? '✅ COMPLIANT' : '❌ NON-COMPLIANT';
  const scoreColor = result.score >= 90 ? '🟢' : result.score >= 70 ? '🟡' : '🔴';
  
  console.log(`${status} ${scoreColor} ${result.score}%\n`);
  
  if (result.violations.length > 0) {
    console.log('🚨 Violations:');
    result.violations.forEach((violation, index) => {
      console.log(`   ${index + 1}. ${violation}`);
    });
    console.log('');
  }
  
  if (result.warnings.length > 0) {
    console.log('⚠️  Warnings:');
    result.warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. ${warning}`);
    });
    console.log('');
  }
  
  if (result.violations.length > 0) {
    console.log('🔧 Recommended Fix:');
    console.log(`   npm run header:inject --file "${filePath}"`);
  }
  
  return result.violations.length === 0;
}

/**
 * Pre-commit compliance check
 */
function preCommitCheck() {
  console.log('🔒 Pre-commit Header Compliance Check');
  console.log('=====================================\n');
  
  // Check staged files (simplified for demo)
  const results = validateFiles('**/*.ts', { 
    verbose: false, 
    summary: true,
    exclude: ['node_modules/**', 'dist/**', '**/*.test.ts', '**/__tests__/**']
  });
  
  if (results && results.nonCompliant > 0) {
    console.log('\n🚫 COMMIT BLOCKED - Header compliance violations detected');
    console.log('Fix violations before committing:');
    generateQuickFixes(results);
    return false;
  }
  
  console.log('\n✅ All files pass header compliance check');
  return true;
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('🎯 OA Framework Header Compliance Checker');
    console.log('==========================================\n');
    console.log('Usage:');
    console.log('  npm run header:check                    # Check all TypeScript files');
    console.log('  npm run header:check --file <path>      # Check specific file');
    console.log('  npm run header:check --m01              # Check M0.1 milestone files');
    console.log('  npm run header:check --dir <directory>  # Check specific directory');
    console.log('  npm run header:check --pre-commit       # Pre-commit validation');
    console.log('');
    
    // Default: check all files
    const results = validateFiles('**/*.ts', { 
      verbose: false, 
      summary: true,
      exclude: ['node_modules/**', 'dist/**', '**/*.test.ts', '**/__tests__/**']
    });
    
    if (results) {
      generateQuickFixes(results);
    }
    
  } else {
    const command = args[0];
    
    switch (command) {
      case '--file':
        if (args[1]) {
          checkSingleFile(args[1]);
        } else {
          console.error('❌ Please specify a file path');
        }
        break;
        
      case '--m01':
        const m01Results = checkM01Compliance();
        generateQuickFixes(m01Results);
        break;
        
      case '--dir':
        if (args[1]) {
          const dirResults = checkDirectoryCompliance(args[1]);
          generateQuickFixes(dirResults);
        } else {
          console.error('❌ Please specify a directory path');
        }
        break;
        
      case '--pre-commit':
        const passed = preCommitCheck();
        process.exit(passed ? 0 : 1);
        break;
        
      case '--pattern':
        if (args[1]) {
          const results = validateFiles(args[1], { verbose: false, summary: true });
          generateQuickFixes(results);
        } else {
          console.error('❌ Please specify a file pattern');
        }
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use --help for usage information');
    }
  }
}

module.exports = {
  checkDirectoryCompliance,
  checkM01Compliance,
  checkSingleFile,
  generateQuickFixes,
  preCommitCheck
};
