#!/usr/bin/env node

/**
 * ============================================================================
 * OA FRAMEWORK - Header Generation Tool
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file Header Generation Tool for OA Framework
 * @description Interactive and automated header generation for unified format
 * @version 1.0.0
 * @created 2025-09-12
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { generateUnifiedHeader } = require('./inject-header');

/**
 * Predefined templates for common component types
 */
const COMPONENT_TEMPLATES = {
  'enhanced-service': {
    template: 'enhanced-service',
    complexity: 'Complex',
    category: 'core',
    tier: 'server',
    context: 'platform',
    performanceTarget: '<10ms',
    memoryFootprint: '<50MB'
  },
  'base-component': {
    template: 'base-component',
    complexity: 'Moderate',
    category: 'foundation',
    tier: 'shared',
    context: 'base',
    performanceTarget: '<20ms',
    memoryFootprint: '<25MB'
  },
  'tracking-component': {
    template: 'tracking-component',
    complexity: 'Complex',
    category: 'tracking',
    tier: 'server',
    context: 'platform',
    performanceTarget: '<5ms',
    memoryFootprint: '<100MB'
  },
  'governance-component': {
    template: 'governance-component',
    complexity: 'Complex',
    category: 'governance',
    tier: 'server',
    context: 'platform',
    performanceTarget: '<15ms',
    memoryFootprint: '<75MB'
  },
  'client-component': {
    template: 'client-component',
    complexity: 'Moderate',
    category: 'ui',
    tier: 'client',
    context: 'frontend',
    performanceTarget: '<100ms',
    memoryFootprint: '<10MB'
  }
};

/**
 * Interactive header generation wizard
 */
async function headerGenerationWizard() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt) => new Promise(resolve => rl.question(prompt, resolve));

  console.log('🎯 OA Framework Unified Header Generation Wizard');
  console.log('================================================\n');

  try {
    // Component selection
    console.log('📋 Available Component Templates:');
    Object.keys(COMPONENT_TEMPLATES).forEach((key, index) => {
      console.log(`   ${index + 1}. ${key}`);
    });
    console.log('   6. custom (manual configuration)\n');

    const templateChoice = await question('Select template (1-6): ');
    const templateKeys = Object.keys(COMPONENT_TEMPLATES);
    let selectedTemplate = null;

    if (templateChoice >= 1 && templateChoice <= 5) {
      selectedTemplate = COMPONENT_TEMPLATES[templateKeys[templateChoice - 1]];
      console.log(`✅ Selected: ${templateKeys[templateChoice - 1]}\n`);
    }

    // Basic information
    const componentName = await question('Component Name: ');
    const purpose = await question('Component Purpose: ');
    const description = await question('Detailed Description: ') || purpose;
    
    // File information
    const filepath = await question('File Path (relative to project root): ');
    const milestone = await question('Milestone (default: M0.1): ') || 'M0.1';
    const taskId = await question('Task ID: ');
    
    // Template-specific or custom configuration
    let config = {};
    if (selectedTemplate) {
      config = { ...selectedTemplate };
      console.log('\n📝 Using template defaults. Press Enter to accept or provide custom values:');
      
      const customTier = await question(`Tier (${config.tier}): `);
      if (customTier) config.tier = customTier;
      
      const customCategory = await question(`Category (${config.category}): `);
      if (customCategory) config.category = customCategory;
      
      const customComplexity = await question(`Complexity (${config.complexity}): `);
      if (customComplexity) config.complexity = customComplexity;
      
    } else {
      console.log('\n📝 Custom Configuration:');
      config.tier = await question('Tier (server/client/shared): ') || 'server';
      config.category = await question('Category: ') || 'core';
      config.complexity = await question('Complexity (Simple/Moderate/Complex): ') || 'Moderate';
      config.template = await question('Template type: ') || 'custom-component';
      config.context = await question('Context: ') || 'platform';
    }

    // Advanced options
    const advancedConfig = await question('\nConfigure advanced options? (y/N): ');
    if (advancedConfig.toLowerCase() === 'y') {
      const component = await question('Component ID: ') || componentName.toLowerCase().replace(/\s+/g, '-');
      const reference = await question('Context Reference: ') || 'context-reference';
      const version = await question('Version (default: 1.0.0): ') || '1.0.0';
      
      config.component = component;
      config.reference = reference;
      config.version = version;
    }

    // Generate header
    const options = {
      componentName,
      purpose,
      description,
      filepath,
      milestone,
      taskId,
      ...config
    };

    console.log('\n🔄 Generating unified header...\n');
    const header = generateUnifiedHeader(options);
    
    console.log('📋 Generated Unified Header:');
    console.log('============================\n');
    console.log(header);

    // Save options
    const saveChoice = await question('\nSave header? (1=to file, 2=to clipboard, 3=both, N=no): ');
    
    if (saveChoice === '1' || saveChoice === '3') {
      const outputFile = await question('Output filename: ');
      fs.writeFileSync(outputFile, header);
      console.log(`✅ Header saved to ${outputFile}`);
    }
    
    if (saveChoice === '2' || saveChoice === '3') {
      // Copy to clipboard (simplified - would need clipboard package)
      console.log('📋 Header copied to clipboard (feature requires clipboard package)');
    }

    // Generate template for future use
    const saveTemplate = await question('\nSave as reusable template? (y/N): ');
    if (saveTemplate.toLowerCase() === 'y') {
      const templateName = await question('Template name: ');
      const templateData = {
        name: templateName,
        options,
        created: new Date().toISOString()
      };
      
      const templatesDir = path.join(__dirname, 'templates');
      if (!fs.existsSync(templatesDir)) {
        fs.mkdirSync(templatesDir, { recursive: true });
      }
      
      const templateFile = path.join(templatesDir, `${templateName}.json`);
      fs.writeFileSync(templateFile, JSON.stringify(templateData, null, 2));
      console.log(`✅ Template saved as ${templateFile}`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

/**
 * Generate header from template file
 */
function generateFromTemplate(templateName) {
  const templatesDir = path.join(__dirname, 'templates');
  const templateFile = path.join(templatesDir, `${templateName}.json`);
  
  if (!fs.existsSync(templateFile)) {
    console.error(`❌ Template not found: ${templateName}`);
    console.log('Available templates:');
    
    if (fs.existsSync(templatesDir)) {
      const templates = fs.readdirSync(templatesDir)
        .filter(f => f.endsWith('.json'))
        .map(f => f.replace('.json', ''));
      
      templates.forEach(t => console.log(`   - ${t}`));
    } else {
      console.log('   No templates found. Create one with --wizard');
    }
    return;
  }
  
  const templateData = JSON.parse(fs.readFileSync(templateFile, 'utf8'));
  const header = generateUnifiedHeader(templateData.options);
  
  console.log('📋 Generated Header from Template:');
  console.log('==================================\n');
  console.log(header);
  
  return header;
}

/**
 * Quick header generation with minimal prompts
 */
async function quickGeneration() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt) => new Promise(resolve => rl.question(prompt, resolve));

  console.log('⚡ Quick Header Generation');
  console.log('=========================\n');

  try {
    const componentName = await question('Component Name: ');
    const filepath = await question('File Path: ');
    const taskId = await question('Task ID: ');
    
    const options = {
      componentName,
      filepath,
      taskId,
      purpose: `${componentName} component`,
      description: `${componentName} component for OA Framework`,
      milestone: 'M0.1',
      tier: 'server',
      category: 'core',
      complexity: 'Moderate'
    };

    const header = generateUnifiedHeader(options);
    console.log('\n📋 Generated Header:\n');
    console.log(header);

    const save = await question('\nSave to file? (y/N): ');
    if (save.toLowerCase() === 'y') {
      const filename = await question('Filename: ');
      fs.writeFileSync(filename, header);
      console.log(`✅ Saved to ${filename}`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

/**
 * List available templates
 */
function listTemplates() {
  console.log('📋 Available Header Templates');
  console.log('=============================\n');
  
  console.log('Built-in Templates:');
  Object.keys(COMPONENT_TEMPLATES).forEach(template => {
    const config = COMPONENT_TEMPLATES[template];
    console.log(`   📄 ${template}`);
    console.log(`      Complexity: ${config.complexity}`);
    console.log(`      Category: ${config.category}`);
    console.log(`      Tier: ${config.tier}`);
    console.log('');
  });
  
  const templatesDir = path.join(__dirname, 'templates');
  if (fs.existsSync(templatesDir)) {
    const customTemplates = fs.readdirSync(templatesDir)
      .filter(f => f.endsWith('.json'));
    
    if (customTemplates.length > 0) {
      console.log('Custom Templates:');
      customTemplates.forEach(template => {
        const name = template.replace('.json', '');
        console.log(`   📄 ${name} (custom)`);
      });
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    headerGenerationWizard();
  } else {
    const command = args[0];
    
    switch (command) {
      case '--wizard':
        headerGenerationWizard();
        break;
        
      case '--quick':
        quickGeneration();
        break;
        
      case '--template':
        if (args[1]) {
          generateFromTemplate(args[1]);
        } else {
          console.error('❌ Please specify a template name');
          listTemplates();
        }
        break;
        
      case '--list':
        listTemplates();
        break;
        
      case '--help':
        console.log('OA Framework Header Generation Tool');
        console.log('Usage: node generate-header.js [options]');
        console.log('');
        console.log('Options:');
        console.log('  (no args)           Interactive wizard (default)');
        console.log('  --wizard            Full interactive wizard');
        console.log('  --quick             Quick generation with minimal prompts');
        console.log('  --template <name>   Generate from saved template');
        console.log('  --list              List available templates');
        console.log('  --help              Show this help message');
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use --help for usage information');
    }
  }
}

module.exports = {
  headerGenerationWizard,
  generateFromTemplate,
  quickGeneration,
  listTemplates,
  COMPONENT_TEMPLATES
};
